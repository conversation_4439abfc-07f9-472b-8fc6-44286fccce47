<template>
  <div class="homepage">
    <!-- Header -->
    <header class="header">
      <nav class="nav-container">
        <div class="logo">
          <h2>🌱 AgriSmart</h2>
        </div>
        <ul class="nav-menu" :class="{ active: isMenuOpen }">
          <li><a href="#home" @click="closeMenu">Home</a></li>
          <li><a href="#products" @click="closeMenu">Products</a></li>
          <li><a href="#features" @click="closeMenu">Features</a></li>
          <li><a href="#about" @click="closeMenu">About</a></li>
          <li><a href="#contact" @click="closeMenu">Contact</a></li>
        </ul>
        <div class="auth-buttons">
          <button class="btn-secondary">Login</button>
          <button class="btn-primary">Register</button>
        </div>
        <div class="hamburger" @click="toggleMenu" :class="{ active: isMenuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1>Smart Agriculture, Smarter Commerce</h1>
          <p>Connect directly with farmers and consumers through our intelligent agricultural marketplace. Fresh produce, fair prices, sustainable farming.</p>
          <div class="hero-buttons">
            <button class="btn-primary large">Start Shopping</button>
            <button class="btn-secondary large">Sell Your Products</button>
          </div>
        </div>
        <div class="hero-image">
          <div class="hero-placeholder">
            <span>🚜</span>
            <p>Smart Farming Technology</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Products -->
    <section id="products" class="featured-products">
      <div class="container">
        <h2>Featured Products</h2>
        <p class="section-subtitle">Fresh from farm to your table</p>
        <div class="products-grid">
          <div class="product-card" v-for="product in featuredProducts" :key="product.id">
            <div class="product-image">
              <span class="product-icon">{{ product.icon }}</span>
            </div>
            <div class="product-info">
              <h3>{{ product.name }}</h3>
              <p class="product-price">${{ product.price }}/{{ product.unit }}</p>
              <p class="product-farmer">by {{ product.farmer }}</p>
              <button class="btn-primary small">Add to Cart</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Key Features -->
    <section id="features" class="features">
      <div class="container">
        <h2>Why Choose AgriSmart?</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Statistics -->
    <section class="stats">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-item" v-for="stat in statistics" :key="stat.id">
            <h3>{{ stat.number }}</h3>
            <p>{{ stat.label }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>🌱 AgriSmart</h3>
            <p>Connecting farmers and consumers through intelligent agricultural commerce.</p>
          </div>
          <div class="footer-section">
            <h4>Quick Links</h4>
            <ul>
              <li><a href="#products">Products</a></li>
              <li><a href="#features">Features</a></li>
              <li><a href="#about">About Us</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Support</h4>
            <ul>
              <li><a href="#">Help Center</a></li>
              <li><a href="#">Shipping Info</a></li>
              <li><a href="#">Returns</a></li>
              <li><a href="#">FAQ</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Contact Info</h4>
            <p>📧 <EMAIL></p>
            <p>📞 +****************</p>
            <p>📍 123 Farm Street, Agriculture City</p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 AgriSmart. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'HomePage',
  setup() {
    const isMenuOpen = ref(false)
    
    const featuredProducts = ref([
      { id: 1, name: 'Organic Tomatoes', price: '4.99', unit: 'lb', farmer: 'Green Valley Farm', icon: '🍅' },
      { id: 2, name: 'Fresh Lettuce', price: '2.49', unit: 'head', farmer: 'Sunny Acres', icon: '🥬' },
      { id: 3, name: 'Sweet Corn', price: '3.99', unit: 'dozen', farmer: 'Harvest Hills', icon: '🌽' },
      { id: 4, name: 'Farm Eggs', price: '5.99', unit: 'dozen', farmer: 'Happy Hens Farm', icon: '🥚' },
      { id: 5, name: 'Organic Carrots', price: '3.49', unit: 'lb', farmer: 'Root & Branch', icon: '🥕' },
      { id: 6, name: 'Fresh Apples', price: '4.49', unit: 'lb', farmer: 'Orchard View', icon: '🍎' }
    ])

    const features = ref([
      { id: 1, icon: '🚚', title: 'Fast Delivery', description: 'Same-day delivery from local farms to your doorstep' },
      { id: 2, icon: '🌿', title: 'Organic Certified', description: 'All products are certified organic and sustainably grown' },
      { id: 3, icon: '💰', title: 'Fair Pricing', description: 'Direct from farmer pricing ensures fair value for everyone' },
      { id: 4, icon: '📱', title: 'Smart Platform', description: 'AI-powered recommendations and inventory management' }
    ])

    const statistics = ref([
      { id: 1, number: '10K+', label: 'Happy Customers' },
      { id: 2, number: '500+', label: 'Partner Farms' },
      { id: 3, number: '50K+', label: 'Products Sold' },
      { id: 4, number: '98%', label: 'Satisfaction Rate' }
    ])

    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value
    }

    const closeMenu = () => {
      isMenuOpen.value = false
    }

    onMounted(() => {
      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault()
          const target = document.querySelector(this.getAttribute('href'))
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' })
          }
        })
      })
    })

    return {
      isMenuOpen,
      featuredProducts,
      features,
      statistics,
      toggleMenu,
      closeMenu
    }
  }
}
</script>

<style scoped>
/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.homepage {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Buttons */
.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #4CAF50;
  border: 2px solid #4CAF50;
}

.btn-secondary:hover {
  background: #4CAF50;
  color: white;
}

.btn-primary.large, .btn-secondary.large {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-primary.small {
  padding: 8px 16px;
  font-size: 14px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo h2 {
  color: #4CAF50;
  font-size: 24px;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #4CAF50;
}

.auth-buttons {
  display: flex;
  gap: 15px;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
  padding: 120px 0 80px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c5530;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-text p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-placeholder {
  background: white;
  border-radius: 20px;
  padding: 60px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 3px solid #e8f5e8;
}

.hero-placeholder span {
  font-size: 4rem;
  display: block;
  margin-bottom: 20px;
}

.hero-placeholder p {
  color: #4CAF50;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Featured Products */
.featured-products {
  padding: 80px 0;
  background: white;
}

.featured-products h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.section-subtitle {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 50px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
  text-align: center;
  margin-bottom: 20px;
}

.product-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
}

.product-info h3 {
  font-size: 1.3rem;
  color: #2c5530;
  margin-bottom: 8px;
}

.product-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 5px;
}

.product-farmer {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

/* Features Section */
.features {
  padding: 80px 0;
  background: #f8fffe;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 50px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.4rem;
  color: #2c5530;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Statistics */
.stats {
  padding: 60px 0;
  background: #4CAF50;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  text-align: center;
}

.stat-item h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-item p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Footer */
.footer {
  background: #2c5530;
  color: white;
  padding: 50px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3, .footer-section h4 {
  margin-bottom: 20px;
  color: #4CAF50;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #4CAF50;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #444;
  color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 20px 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .auth-buttons {
    display: none;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary.large, .btn-secondary.large {
    width: 100%;
    max-width: 300px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card, .feature-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
button:focus, a:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}
</style>
